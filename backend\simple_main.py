"""
Simplified FastAPI backend for AI Email Tagger
This version works without Gmail/OpenAI dependencies for testing
"""

from fastapi import FastAP<PERSON>, Depends, HTTPException, Query
from fastapi.middleware.cors import CORSMiddleware
from sqlalchemy.orm import Session
from datetime import datetime, timed<PERSON><PERSON>
from typing import List
import json

# Import local modules
from database import get_db, create_tables, EmailRecord
from models import (
    EmailListResponse, StatsResponse, TrendData, ActivityItem, 
    SystemStatusResponse, ReclassifyRequest, EmailResponse
)

# Create FastAPI app
app = FastAPI(
    title="AI Email Tagger API",
    description="Backend API for AI-powered email classification and management",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://127.0.0.1:3000"],  # React app
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize database on startup
@app.on_event("startup")
async def startup_event():
    print("Starting AI Email Tagger API...")
    create_tables()
    print("Database initialized")
    
    # Add some sample data for testing
    add_sample_data()

def add_sample_data():
    """Add sample email data for testing the frontend"""
    from database import SessionLocal
    
    db = SessionLocal()
    
    # Check if we already have data
    if db.query(EmailRecord).count() > 0:
        db.close()
        return
    
    sample_emails = [
        {
            "id": "email_1",
            "subject": "Q4 Budget Review Meeting",
            "sender": "<EMAIL>",
            "sender_name": "Finance Team",
            "snippet": "Please join us for the quarterly budget review meeting scheduled for next Tuesday at 2 PM in the conference room.",
            "labels": ["work", "important"],
            "timestamp": datetime.now() - timedelta(minutes=5),
            "is_read": False,
            "processing_time_ms": 150
        },
        {
            "id": "email_2", 
            "subject": "Your Amazon order has shipped",
            "sender": "<EMAIL>",
            "sender_name": "Amazon",
            "snippet": "Good news! Your order #123-4567890 has been shipped and is on its way. Track your package using the link below.",
            "labels": ["personal", "shopping"],
            "timestamp": datetime.now() - timedelta(minutes=15),
            "is_read": True,
            "processing_time_ms": 120
        },
        {
            "id": "email_3",
            "subject": "Weekly Tech Newsletter",
            "sender": "<EMAIL>", 
            "sender_name": "TechCrunch",
            "snippet": "This week in tech: AI breakthroughs, startup funding rounds, and the latest gadget reviews from our team.",
            "labels": ["promotional", "tech"],
            "timestamp": datetime.now() - timedelta(minutes=30),
            "is_read": True,
            "processing_time_ms": 95
        },
        {
            "id": "email_4",
            "subject": "URGENT: You have won $1,000,000!!!",
            "sender": "<EMAIL>",
            "sender_name": "Fake Lottery",
            "snippet": "Congratulations! You are our lucky winner. Click here to claim your prize immediately before it expires.",
            "labels": ["spam"],
            "timestamp": datetime.now() - timedelta(minutes=45),
            "is_read": False,
            "processing_time_ms": 200
        },
        {
            "id": "email_5",
            "subject": "Family dinner this Sunday",
            "sender": "<EMAIL>",
            "sender_name": "Mom",
            "snippet": "Hi honey, don't forget about our family dinner this Sunday at 6 PM. Your dad is making his famous lasagna!",
            "labels": ["personal", "family"],
            "timestamp": datetime.now() - timedelta(hours=2),
            "is_read": True,
            "processing_time_ms": 110
        },
        {
            "id": "email_6",
            "subject": "Project deadline reminder",
            "sender": "<EMAIL>",
            "sender_name": "Project Manager",
            "snippet": "Just a friendly reminder that the project deliverables are due this Friday. Please let me know if you need any help.",
            "labels": ["work", "important"],
            "timestamp": datetime.now() - timedelta(hours=4),
            "is_read": False,
            "processing_time_ms": 130
        },
        {
            "id": "email_7",
            "subject": "Social media notifications",
            "sender": "<EMAIL>",
            "sender_name": "LinkedIn",
            "snippet": "You have 3 new connection requests and 5 profile views this week. See who's been checking out your profile.",
            "labels": ["social"],
            "timestamp": datetime.now() - timedelta(hours=6),
            "is_read": True,
            "processing_time_ms": 85
        }
    ]
    
    for email_data in sample_emails:
        email_record = EmailRecord(**email_data)
        db.add(email_record)
    
    db.commit()
    db.close()
    print("Sample data added to database")

# ============================================================================
# API ENDPOINTS FOR FRONTEND
# ============================================================================

@app.get("/emails/classified", response_model=EmailListResponse)
async def get_classified_emails(
    page: int = Query(1, ge=1),
    limit: int = Query(50, ge=1, le=100),
    search: str = Query(None),
    db: Session = Depends(get_db)
):
    """Get paginated list of classified emails"""
    from email_service import EmailService
    return EmailService.get_classified_emails(db, page, limit, search)

@app.get("/emails/stats", response_model=StatsResponse)
async def get_stats(db: Session = Depends(get_db)):
    """Get email classification statistics"""
    from email_service import EmailService
    return EmailService.get_stats(db)

@app.get("/emails/by-label/{label}", response_model=List[EmailResponse])
async def get_emails_by_label(label: str, db: Session = Depends(get_db)):
    """Get emails filtered by a specific label"""
    from email_service import EmailService
    return EmailService.get_emails_by_label(db, label)

@app.get("/emails/recent", response_model=List[ActivityItem])
async def get_recent_activity(
    timeframe: str = Query("24h", pattern="^(24h|7d|30d)$"),
    db: Session = Depends(get_db)
):
    """Get recent email classification activity"""
    from email_service import EmailService
    return EmailService.get_recent_activity(db, timeframe)

@app.get("/emails/trends", response_model=List[TrendData])
async def get_label_trends(
    days: int = Query(7, ge=1, le=30),
    db: Session = Depends(get_db)
):
    """Get label distribution trends over time"""
    from email_service import EmailService
    return EmailService.get_label_trends(db, days)

@app.post("/emails/{email_id}/reclassify", response_model=EmailResponse)
async def reclassify_email(
    email_id: str,
    request: ReclassifyRequest,
    db: Session = Depends(get_db)
):
    """Manually reclassify an email with new labels"""
    try:
        from email_service import EmailService
        return EmailService.reclassify_email(db, email_id, request.labels)
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to reclassify email: {str(e)}")

@app.get("/system/status", response_model=SystemStatusResponse)
async def get_system_status(db: Session = Depends(get_db)):
    """Get system health and status information"""
    
    # For this simplified version, assume connections are working
    gmail_connected = True
    openai_connected = True
    
    # Get last processed email
    last_email = db.query(EmailRecord).order_by(EmailRecord.timestamp.desc()).first()
    last_processed = last_email.timestamp if last_email else None
    
    # Count emails processed today
    today = datetime.utcnow().date()
    emails_today = db.query(EmailRecord).filter(
        EmailRecord.timestamp >= datetime.combine(today, datetime.min.time())
    ).count()
    
    return SystemStatusResponse(
        status="active",
        gmail_connected=gmail_connected,
        openai_connected=openai_connected,
        last_processed=last_processed,
        emails_processed_today=emails_today
    )

# ============================================================================
# HEALTH CHECK ENDPOINTS
# ============================================================================

@app.get("/health")
async def health_check():
    """Simple health check endpoint"""
    return {"status": "healthy", "timestamp": datetime.utcnow()}

@app.get("/")
async def root():
    """Root endpoint with API information"""
    return {
        "message": "AI Email Tagger API",
        "version": "1.0.0",
        "docs": "/docs",
        "status": "running"
    }

# For running locally
if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000, reload=True)
