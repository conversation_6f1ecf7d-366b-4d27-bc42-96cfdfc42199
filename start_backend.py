#!/usr/bin/env python3
"""
Startup script for the AI Email Tagger Backend
"""

import os
import sys
import subprocess
from pathlib import Path

def check_python_version():
    """Check if Python version is compatible"""
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 or higher is required")
        sys.exit(1)
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor} detected")

def install_requirements():
    """Install required packages"""
    print("📦 Installing requirements...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ Requirements installed successfully")
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install requirements: {e}")
        sys.exit(1)

def setup_environment():
    """Setup environment variables and files"""
    print("🔧 Setting up environment...")
    
    # Check for required environment variables
    required_env_vars = ["OPENAI_API_KEY"]
    missing_vars = []
    
    for var in required_env_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        print("⚠️  Missing environment variables:")
        for var in missing_vars:
            print(f"   - {var}")
        print("\nPlease set these environment variables before running the backend.")
        print("You can create a .env file or export them in your shell.")
        
    # Check for Gmail credentials
    if not Path("backend/credentials.json").exists():
        print("⚠️  Gmail credentials.json not found in backend/ directory")
        print("   Please download your Gmail API credentials and place them in backend/credentials.json")
    
    print("✅ Environment setup complete")

def start_server():
    """Start the FastAPI server"""
    print("🚀 Starting AI Email Tagger Backend...")
    print("   Server will be available at: http://localhost:8000")
    print("   API documentation: http://localhost:8000/docs")
    print("   Press Ctrl+C to stop the server")
    
    try:
        # Change to backend directory
        os.chdir("backend")
        
        # Start the server
        subprocess.run([
            sys.executable, "-m", "uvicorn", 
            "main:app", 
            "--host", "0.0.0.0", 
            "--port", "8000", 
            "--reload"
        ])
    except KeyboardInterrupt:
        print("\n👋 Server stopped")
    except Exception as e:
        print(f"❌ Failed to start server: {e}")
        sys.exit(1)

def main():
    """Main startup function"""
    print("🤖 AI Email Tagger Backend Startup")
    print("=" * 40)
    
    check_python_version()
    install_requirements()
    setup_environment()
    start_server()

if __name__ == "__main__":
    main()
