from pydantic import BaseModel
from typing import List, Optional
from datetime import datetime

class EmailResponse(BaseModel):
    id: str
    subject: str
    sender: str
    sender_name: Optional[str] = None
    snippet: str
    labels: List[str]
    timestamp: datetime
    is_read: bool = False
    processing_time_ms: Optional[int] = None
    
    class Config:
        from_attributes = True

class EmailListResponse(BaseModel):
    emails: List[EmailResponse]
    total_emails: int
    current_page: int
    total_pages: int
    
class StatsResponse(BaseModel):
    total_emails: int
    total_labels: int
    processing_rate: float
    avg_processing_time: int
    emails_change: float = 0.0
    labels_change: float = 0.0
    rate_change: float = 0.0
    time_change: float = 0.0

class TrendData(BaseModel):
    date: str
    work: int = 0
    personal: int = 0
    spam: int = 0
    important: int = 0
    promotional: int = 0
    social: int = 0
    family: int = 0
    shopping: int = 0
    tech: int = 0

class ActivityItem(BaseModel):
    id: str
    type: str = "classification"
    email: EmailResponse
    labels: List[str]
    timestamp: datetime

class SystemStatusResponse(BaseModel):
    status: str
    gmail_connected: bool
    openai_connected: bool
    last_processed: Optional[datetime]
    emails_processed_today: int
    
class ReclassifyRequest(BaseModel):
    labels: List[str]
