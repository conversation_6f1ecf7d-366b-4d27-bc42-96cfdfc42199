# Environment variables for AI Email Tagger Backend
# Copy this file to .env and fill in your actual values

# OpenAI API Key (required for email classification)
OPENAI_API_KEY=your_openai_api_key_here

# Database URL (optional, defaults to SQLite)
DATABASE_URL=sqlite:///./emails.db

# Gmail API Configuration
# Download credentials.json from Google Cloud Console and place in backend/ directory

# Optional: Custom configuration
DEBUG=true
LOG_LEVEL=info
