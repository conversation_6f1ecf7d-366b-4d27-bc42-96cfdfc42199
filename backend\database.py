from sqlalchemy import create_engine, Column, Integer, String, DateTime, Text, Boolean, JSON
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from datetime import datetime
import os

# Database URL - using SQLite for simplicity, can be changed to PostgreSQL for production
DATABASE_URL = os.getenv("DATABASE_URL", "sqlite:///./emails.db")

engine = create_engine(DATABASE_URL, connect_args={"check_same_thread": False} if "sqlite" in DATABASE_URL else {})
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

Base = declarative_base()

class EmailRecord(Base):
    __tablename__ = "emails"
    
    id = Column(String, primary_key=True, index=True)  # Gmail message ID
    subject = Column(String, index=True)
    sender = Column(String, index=True)
    sender_name = Column(String)
    snippet = Column(Text)
    body = Column(Text)
    labels = Column(JSON)  # Store as JSON array
    timestamp = Column(DateTime, default=datetime.utcnow)
    processed_at = Column(DateTime, default=datetime.utcnow)
    is_read = Column(Boolean, default=False)
    thread_id = Column(String)
    
    # Classification metadata
    classification_confidence = Column(String)  # High, Medium, Low
    processing_time_ms = Column(Integer)
    
class SystemStats(Base):
    __tablename__ = "system_stats"
    
    id = Column(Integer, primary_key=True, index=True)
    date = Column(DateTime, default=datetime.utcnow)
    total_emails_processed = Column(Integer, default=0)
    total_labels_applied = Column(Integer, default=0)
    avg_processing_time = Column(Integer, default=0)  # in milliseconds
    processing_rate = Column(Integer, default=0)  # percentage
    
class LabelStats(Base):
    __tablename__ = "label_stats"
    
    id = Column(Integer, primary_key=True, index=True)
    date = Column(DateTime, default=datetime.utcnow)
    label_name = Column(String, index=True)
    count = Column(Integer, default=0)

# Create tables
def create_tables():
    Base.metadata.create_all(bind=engine)

# Dependency to get DB session
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()
