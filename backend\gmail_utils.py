# Gmail utility functions
# This is a placeholder for your existing gmail_utils.py file
# You should copy your actual implementation here

import os
from google.auth.transport.requests import Request
from google.oauth2.credentials import Credentials
from google_auth_oauthlib.flow import InstalledAppFlow
from googleapiclient.discovery import build

# Gmail API scopes
SCOPES = ['https://www.googleapis.com/auth/gmail.modify']

def authenticate_gmail():
    """Authenticate and return Gmail service object"""
    creds = None
    # The file token.json stores the user's access and refresh tokens.
    if os.path.exists('token.json'):
        creds = Credentials.from_authorized_user_file('token.json', SCOPES)
    
    # If there are no (valid) credentials available, let the user log in.
    if not creds or not creds.valid:
        if creds and creds.expired and creds.refresh_token:
            creds.refresh(Request())
        else:
            flow = InstalledAppFlow.from_client_secrets_file('credentials.json', SCOPES)
            creds = flow.run_local_server(port=0)
        # Save the credentials for the next run
        with open('token.json', 'w') as token:
            token.write(creds.to_json())

    service = build('gmail', 'v1', credentials=creds)
    return service

def get_emails(service, query='', max_results=10):
    """Get emails based on query"""
    try:
        results = service.users().messages().list(userId='me', q=query, maxResults=max_results).execute()
        messages = results.get('messages', [])
        
        emails = []
        for message in messages:
            msg = service.users().messages().get(userId='me', id=message['id']).execute()
            
            # Extract email data
            email_data = {
                'id': msg['id'],
                'threadId': msg['threadId'],
                'snippet': msg.get('snippet', ''),
                'timestamp': msg.get('internalDate', ''),
            }
            
            # Extract headers
            headers = msg['payload'].get('headers', [])
            for header in headers:
                name = header['name'].lower()
                if name == 'subject':
                    email_data['subject'] = header['value']
                elif name == 'from':
                    email_data['sender'] = header['value']
                    # Extract sender name
                    if '<' in header['value']:
                        email_data['senderName'] = header['value'].split('<')[0].strip().strip('"')
                    else:
                        email_data['senderName'] = header['value']
            
            emails.append(email_data)
        
        return emails
    except Exception as error:
        print(f'An error occurred: {error}')
        return []

def get_emails_by_ids(service, message_ids):
    """Get emails by their message IDs"""
    emails = []
    for msg_id in message_ids:
        try:
            msg = service.users().messages().get(userId='me', id=msg_id).execute()
            
            # Extract email data
            email_data = {
                'id': msg['id'],
                'threadId': msg['threadId'],
                'snippet': msg.get('snippet', ''),
                'timestamp': msg.get('internalDate', ''),
            }
            
            # Extract headers
            headers = msg['payload'].get('headers', [])
            for header in headers:
                name = header['name'].lower()
                if name == 'subject':
                    email_data['subject'] = header['value']
                elif name == 'from':
                    email_data['sender'] = header['value']
                    # Extract sender name
                    if '<' in header['value']:
                        email_data['senderName'] = header['value'].split('<')[0].strip().strip('"')
                    else:
                        email_data['senderName'] = header['value']
            
            emails.append(email_data)
        except Exception as error:
            print(f'Error fetching email {msg_id}: {error}')
    
    return emails

def add_labels_to_email(service, message_id, labels):
    """Add labels to an email"""
    try:
        # Convert labels to Gmail label format
        gmail_labels = []
        for label in labels:
            # Map your custom labels to Gmail labels or create custom ones
            if label.lower() == 'spam':
                gmail_labels.append('SPAM')
            elif label.lower() == 'important':
                gmail_labels.append('IMPORTANT')
            else:
                # For custom labels, you might need to create them first
                gmail_labels.append(f'CATEGORY_{label.upper()}')
        
        if gmail_labels:
            service.users().messages().modify(
                userId='me',
                id=message_id,
                body={'addLabelIds': gmail_labels}
            ).execute()
            
        print(f"Added labels {labels} to email {message_id}")
    except Exception as error:
        print(f'Error adding labels to email {message_id}: {error}')
