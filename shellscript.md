# 1. <PERSON><PERSON>/Copy the project
git clone <your-repo> AI-tagger
# OR copy the entire project folder

cd "AI tagger"  # Note the space in folder name

# 2. Install Node.js dependencies
npm install

# 3. Create Python virtual environment
python3 -m venv backend_env

# 4. Activate virtual environment (macOS/Linux)
source backend_env/bin/activate
# On Windows: backend_env\Scripts\activate

# 5. Install Python dependencies
pip install fastapi uvicorn sqlalchemy

# 6. Set up environment variables (optional for demo)
# Create backend/.env file with:
# OPENAI_API_KEY=your_key_here