# AI Email Tagger Frontend

A React-based dashboard for monitoring and managing AI-powered email classification.

## Features

- **Real-time Dashboard**: View email classification statistics and trends
- **Email Management**: Browse and filter classified emails
- **Visual Analytics**: Charts and graphs showing classification patterns
- **Responsive Design**: Modern UI that works on desktop and mobile

## Getting Started

### Prerequisites

- Node.js (v14 or higher)
- npm or yarn
- FastAPI backend running on port 8000

### Installation

1. Install dependencies:
```bash
npm install
```

2. Start the development server:
```bash
npm start
```

The application will open at `http://localhost:3000`.

### Backend Integration

This frontend is designed to work with a FastAPI backend. Make sure your backend implements the following endpoints:

- `GET /emails/classified` - Get paginated classified emails
- `GET /emails/stats` - Get classification statistics
- `GET /emails/by-label/{label}` - Get emails by specific label
- `GET /emails/recent` - Get recent activity
- `GET /emails/trends` - Get classification trends over time
- `POST /emails/{email_id}/reclassify` - Reclassify an email
- `GET /system/status` - Get system health status

### Sample Backend Endpoints

The frontend expects the following data structures:

#### Stats Response (`/emails/stats`)
```json
{
  "totalEmails": 1250,
  "totalLabels": 8,
  "processingRate": 98.5,
  "avgProcessingTime": 150,
  "emailsChange": 12.5,
  "labelsChange": 0,
  "rateChange": 2.1,
  "timeChange": -5.2
}
```

#### Classified Emails Response (`/emails/classified`)
```json
{
  "emails": [
    {
      "id": "email_123",
      "subject": "Meeting reminder",
      "sender": "<EMAIL>",
      "senderName": "John Doe",
      "snippet": "Don't forget about our meeting tomorrow...",
      "labels": ["work", "important"],
      "timestamp": "2024-01-15T10:30:00Z",
      "isRead": false
    }
  ],
  "totalPages": 10,
  "currentPage": 1,
  "totalEmails": 200
}
```

## Available Scripts

- `npm start` - Runs the app in development mode
- `npm build` - Builds the app for production
- `npm test` - Launches the test runner
- `npm eject` - Ejects from Create React App (one-way operation)

## Technologies Used

- **React 18** - Frontend framework
- **Tailwind CSS** - Styling and responsive design
- **Recharts** - Data visualization
- **Lucide React** - Icons
- **Axios** - HTTP client for API calls

## Project Structure

```
src/
├── components/
│   ├── Dashboard.js          # Main dashboard component
│   ├── StatsCards.js         # Statistics cards
│   ├── EmailChart.js         # Email trends chart
│   ├── RecentActivity.js     # Recent activity feed
│   └── EmailList.js          # Email list with filtering
├── services/
│   └── api.js               # API service layer
├── App.js                   # Main app component
├── index.js                 # App entry point
└── index.css               # Global styles
```

## Customization

### Adding New Labels

To add support for new email labels, update the color mappings in:
- `src/components/EmailChart.js`
- `src/components/RecentActivity.js`
- `src/components/EmailList.js`

### Styling

The app uses Tailwind CSS. You can customize the theme in `tailwind.config.js`.

## Production Deployment

1. Build the production version:
```bash
npm run build
```

2. Serve the `build` folder using a static file server or deploy to your preferred hosting platform.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is licensed under the MIT License.
