import React, { useState, useEffect } from 'react';
import { emailAPI } from '../services/api';
import { Mail, Search, Filter, ChevronLeft, ChevronRight, Tag, Calendar, User } from 'lucide-react';

const EmailList = () => {
  const [emails, setEmails] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedLabel, setSelectedLabel] = useState('all');
  // const [sortBy, setSortBy] = useState('date'); // TODO: Implement sorting functionality

  const itemsPerPage = 20;

  const fetchEmails = async (page = 1, search = '', label = 'all') => {
    try {
      setLoading(true);
      setError(null);

      let emailData;
      if (label === 'all') {
        emailData = await emailAPI.getClassifiedEmails(page, itemsPerPage);
      } else {
        emailData = await emailAPI.getEmailsByLabel(label);
      }

      setEmails(emailData.emails || []);
      setTotalPages(emailData.totalPages || 1);
      setCurrentPage(page);
    } catch (err) {
      setError(err.message);
      console.error('Error fetching emails:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchEmails(currentPage, searchTerm, selectedLabel);
  }, [currentPage, searchTerm, selectedLabel]);

  // Sample data for demonstration
  const sampleEmails = [
    {
      id: 'email_1',
      subject: 'Q4 Budget Review Meeting',
      sender: '<EMAIL>',
      senderName: 'Finance Team',
      snippet: 'Please join us for the quarterly budget review meeting scheduled for next Tuesday...',
      labels: ['work', 'important'],
      timestamp: new Date('2024-01-15T10:30:00'),
      isRead: false,
    },
    {
      id: 'email_2',
      subject: 'Your Amazon order has shipped',
      sender: '<EMAIL>',
      senderName: 'Amazon',
      snippet: 'Good news! Your order #123-4567890 has been shipped and is on its way...',
      labels: ['personal', 'shopping'],
      timestamp: new Date('2024-01-15T09:15:00'),
      isRead: true,
    },
    {
      id: 'email_3',
      subject: 'Weekly Tech Newsletter',
      sender: '<EMAIL>',
      senderName: 'TechCrunch',
      snippet: 'This week in tech: AI breakthroughs, startup funding rounds, and more...',
      labels: ['promotional', 'tech'],
      timestamp: new Date('2024-01-15T08:00:00'),
      isRead: true,
    },
    {
      id: 'email_4',
      subject: 'URGENT: You have won $1,000,000!!!',
      sender: '<EMAIL>',
      senderName: 'Fake Lottery',
      snippet: 'Congratulations! You are our lucky winner. Click here to claim your prize...',
      labels: ['spam'],
      timestamp: new Date('2024-01-15T07:45:00'),
      isRead: false,
    },
    {
      id: 'email_5',
      subject: 'Family dinner this Sunday',
      sender: '<EMAIL>',
      senderName: 'Mom',
      snippet: 'Hi honey, don\'t forget about our family dinner this Sunday at 6 PM...',
      labels: ['personal', 'family'],
      timestamp: new Date('2024-01-14T20:30:00'),
      isRead: true,
    },
  ];

  const displayEmails = emails.length > 0 ? emails : sampleEmails;

  const getLabelColor = (label) => {
    const colors = {
      work: 'bg-blue-100 text-blue-800',
      personal: 'bg-green-100 text-green-800',
      spam: 'bg-red-100 text-red-800',
      important: 'bg-yellow-100 text-yellow-800',
      promotional: 'bg-purple-100 text-purple-800',
      social: 'bg-pink-100 text-pink-800',
      family: 'bg-indigo-100 text-indigo-800',
      shopping: 'bg-orange-100 text-orange-800',
      tech: 'bg-cyan-100 text-cyan-800',
    };
    return colors[label] || 'bg-gray-100 text-gray-800';
  };

  const formatDate = (date) => {
    const now = new Date();
    const emailDate = new Date(date);
    const diffInHours = (now - emailDate) / (1000 * 60 * 60);

    if (diffInHours < 24) {
      return emailDate.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } else if (diffInHours < 168) { // 7 days
      return emailDate.toLocaleDateString([], { weekday: 'short' });
    } else {
      return emailDate.toLocaleDateString([], { month: 'short', day: 'numeric' });
    }
  };

  const handlePageChange = (newPage) => {
    if (newPage >= 1 && newPage <= totalPages) {
      setCurrentPage(newPage);
    }
  };

  const uniqueLabels = [...new Set(displayEmails.flatMap(email => email.labels))];

  return (
    <div className="bg-white rounded-lg shadow-sm border">
      {/* Header */}
      <div className="p-6 border-b">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-semibold text-gray-900">Classified Emails</h2>
          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-500">
              {displayEmails.length} emails
            </span>
          </div>
        </div>

        {/* Filters */}
        <div className="flex flex-col sm:flex-row gap-4">
          {/* Search */}
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search emails..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            />
          </div>

          {/* Label Filter */}
          <div className="flex items-center space-x-2">
            <Filter className="h-4 w-4 text-gray-400" />
            <select
              value={selectedLabel}
              onChange={(e) => setSelectedLabel(e.target.value)}
              className="border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            >
              <option value="all">All Labels</option>
              {uniqueLabels.map((label) => (
                <option key={label} value={label}>
                  {label.charAt(0).toUpperCase() + label.slice(1)}
                </option>
              ))}
            </select>
          </div>
        </div>
      </div>

      {/* Email List */}
      <div className="divide-y divide-gray-200">
        {loading ? (
          <div className="p-8 text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto"></div>
            <p className="text-gray-500 mt-2">Loading emails...</p>
          </div>
        ) : error ? (
          <div className="p-8 text-center text-red-600">
            <p>Error loading emails: {error}</p>
          </div>
        ) : displayEmails.length === 0 ? (
          <div className="p-8 text-center text-gray-500">
            <Mail className="h-12 w-12 mx-auto mb-4 text-gray-400" />
            <p>No emails found</p>
          </div>
        ) : (
          displayEmails.map((email) => (
            <div
              key={email.id}
              className={`p-4 hover:bg-gray-50 transition-colors cursor-pointer ${
                !email.isRead ? 'bg-blue-50' : ''
              }`}
            >
              <div className="flex items-start justify-between">
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-3 mb-2">
                    <div className="flex items-center space-x-2">
                      <User className="h-4 w-4 text-gray-400" />
                      <span className="text-sm font-medium text-gray-900">
                        {email.senderName || email.sender}
                      </span>
                      {!email.isRead && (
                        <div className="w-2 h-2 bg-primary-600 rounded-full"></div>
                      )}
                    </div>
                    <div className="flex items-center text-gray-500">
                      <Calendar className="h-3 w-3 mr-1" />
                      <span className="text-xs">{formatDate(email.timestamp)}</span>
                    </div>
                  </div>

                  <h3 className={`text-sm mb-1 ${
                    !email.isRead ? 'font-semibold text-gray-900' : 'font-medium text-gray-700'
                  }`}>
                    {email.subject}
                  </h3>

                  <p className="text-sm text-gray-600 mb-3 line-clamp-2">
                    {email.snippet}
                  </p>

                  <div className="flex flex-wrap gap-1">
                    {email.labels.map((label, index) => (
                      <span
                        key={index}
                        className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getLabelColor(label)}`}
                      >
                        <Tag className="h-3 w-3 mr-1" />
                        {label}
                      </span>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          ))
        )}
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="p-4 border-t flex items-center justify-between">
          <div className="text-sm text-gray-500">
            Page {currentPage} of {totalPages}
          </div>
          <div className="flex items-center space-x-2">
            <button
              onClick={() => handlePageChange(currentPage - 1)}
              disabled={currentPage === 1}
              className="p-2 rounded-md border border-gray-300 disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
            >
              <ChevronLeft className="h-4 w-4" />
            </button>
            <button
              onClick={() => handlePageChange(currentPage + 1)}
              disabled={currentPage === totalPages}
              className="p-2 rounded-md border border-gray-300 disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
            >
              <ChevronRight className="h-4 w-4" />
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default EmailList;
