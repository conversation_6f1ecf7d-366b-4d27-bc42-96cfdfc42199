import React from 'react';
import { Mail, Tag, TrendingUp, Clock } from 'lucide-react';

const StatsCards = ({ stats }) => {
  if (!stats) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {[1, 2, 3, 4].map((i) => (
          <div key={i} className="bg-white p-6 rounded-lg shadow-sm border animate-pulse">
            <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
            <div className="h-8 bg-gray-200 rounded w-1/2"></div>
          </div>
        ))}
      </div>
    );
  }

  const cards = [
    {
      title: 'Total Emails Processed',
      value: stats.totalEmails?.toLocaleString() || '0',
      icon: Mail,
      color: 'bg-blue-500',
      change: stats.emailsChange || 0,
    },
    {
      title: 'Labels Applied',
      value: stats.totalLabels?.toLocaleString() || '0',
      icon: Tag,
      color: 'bg-green-500',
      change: stats.labelsChange || 0,
    },
    {
      title: 'Processing Rate',
      value: `${stats.processingRate || 0}%`,
      icon: TrendingUp,
      color: 'bg-purple-500',
      change: stats.rateChange || 0,
    },
    {
      title: 'Avg Processing Time',
      value: `${stats.avgProcessingTime || 0}ms`,
      icon: Clock,
      color: 'bg-orange-500',
      change: stats.timeChange || 0,
    },
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {cards.map((card, index) => {
        const Icon = card.icon;
        const isPositive = card.change >= 0;
        
        return (
          <div key={index} className="bg-white p-6 rounded-lg shadow-sm border hover:shadow-md transition-shadow">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">{card.title}</p>
                <p className="text-2xl font-bold text-gray-900 mt-1">{card.value}</p>
                {card.change !== 0 && (
                  <div className="flex items-center mt-2">
                    <span className={`text-sm font-medium ${
                      isPositive ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {isPositive ? '+' : ''}{card.change}%
                    </span>
                    <span className="text-sm text-gray-500 ml-1">vs last period</span>
                  </div>
                )}
              </div>
              <div className={`p-3 rounded-full ${card.color}`}>
                <Icon className="h-6 w-6 text-white" />
              </div>
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default StatsCards;
