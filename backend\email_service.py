from sqlalchemy.orm import Session
from sqlalchemy import func, desc, and_
from database import EmailRecord, SystemStats, LabelStats
from models import EmailResponse, EmailListResponse, StatsResponse, TrendData, ActivityItem
from typing import List, Optional
from datetime import datetime, timedelta
import json

class EmailService:
    
    @staticmethod
    def save_email(db: Session, email_data: dict, labels: List[str], processing_time_ms: int = None):
        """Save a classified email to the database"""
        
        # Check if email already exists
        existing_email = db.query(EmailRecord).filter(EmailRecord.id == email_data['id']).first()
        if existing_email:
            # Update existing email with new labels
            existing_email.labels = labels
            existing_email.processed_at = datetime.utcnow()
            if processing_time_ms:
                existing_email.processing_time_ms = processing_time_ms
            db.commit()
            return existing_email
        
        # Create new email record
        email_record = EmailRecord(
            id=email_data['id'],
            subject=email_data.get('subject', ''),
            sender=email_data.get('sender', ''),
            sender_name=email_data.get('senderName', ''),
            snippet=email_data.get('snippet', ''),
            body=email_data.get('body', ''),
            labels=labels,
            timestamp=datetime.fromisoformat(email_data.get('timestamp', datetime.utcnow().isoformat())),
            is_read=email_data.get('isRead', False),
            thread_id=email_data.get('threadId', ''),
            processing_time_ms=processing_time_ms or 0
        )
        
        db.add(email_record)
        db.commit()
        db.refresh(email_record)
        
        # Update statistics
        EmailService.update_stats(db, labels, processing_time_ms)
        
        return email_record
    
    @staticmethod
    def get_classified_emails(db: Session, page: int = 1, limit: int = 50, search: str = None) -> EmailListResponse:
        """Get paginated list of classified emails"""
        query = db.query(EmailRecord)
        
        if search:
            query = query.filter(
                EmailRecord.subject.contains(search) | 
                EmailRecord.sender.contains(search) |
                EmailRecord.snippet.contains(search)
            )
        
        total_emails = query.count()
        emails = query.order_by(desc(EmailRecord.timestamp)).offset((page - 1) * limit).limit(limit).all()
        
        total_pages = (total_emails + limit - 1) // limit
        
        email_responses = [
            EmailResponse(
                id=email.id,
                subject=email.subject,
                sender=email.sender,
                sender_name=email.sender_name,
                snippet=email.snippet,
                labels=email.labels or [],
                timestamp=email.timestamp,
                is_read=email.is_read,
                processing_time_ms=email.processing_time_ms
            ) for email in emails
        ]
        
        return EmailListResponse(
            emails=email_responses,
            total_emails=total_emails,
            current_page=page,
            total_pages=total_pages
        )
    
    @staticmethod
    def get_emails_by_label(db: Session, label: str) -> List[EmailResponse]:
        """Get emails filtered by a specific label"""
        emails = db.query(EmailRecord).filter(
            EmailRecord.labels.contains(f'"{label}"')
        ).order_by(desc(EmailRecord.timestamp)).limit(100).all()
        
        return [
            EmailResponse(
                id=email.id,
                subject=email.subject,
                sender=email.sender,
                sender_name=email.sender_name,
                snippet=email.snippet,
                labels=email.labels or [],
                timestamp=email.timestamp,
                is_read=email.is_read,
                processing_time_ms=email.processing_time_ms
            ) for email in emails
        ]
    
    @staticmethod
    def get_recent_activity(db: Session, timeframe: str = "24h") -> List[ActivityItem]:
        """Get recent email classification activity"""
        if timeframe == "24h":
            since = datetime.utcnow() - timedelta(hours=24)
        elif timeframe == "7d":
            since = datetime.utcnow() - timedelta(days=7)
        else:
            since = datetime.utcnow() - timedelta(hours=24)
        
        emails = db.query(EmailRecord).filter(
            EmailRecord.processed_at >= since
        ).order_by(desc(EmailRecord.processed_at)).limit(20).all()
        
        activities = []
        for email in emails:
            email_response = EmailResponse(
                id=email.id,
                subject=email.subject,
                sender=email.sender,
                sender_name=email.sender_name,
                snippet=email.snippet,
                labels=email.labels or [],
                timestamp=email.timestamp,
                is_read=email.is_read,
                processing_time_ms=email.processing_time_ms
            )
            
            activities.append(ActivityItem(
                id=f"activity_{email.id}",
                type="classification",
                email=email_response,
                labels=email.labels or [],
                timestamp=email.processed_at
            ))
        
        return activities
    
    @staticmethod
    def get_stats(db: Session) -> StatsResponse:
        """Get overall system statistics"""
        total_emails = db.query(EmailRecord).count()
        
        # Count unique labels
        all_labels = db.query(EmailRecord.labels).filter(EmailRecord.labels.isnot(None)).all()
        unique_labels = set()
        for label_tuple in all_labels:
            label_list = label_tuple[0]  # Extract the actual list from the tuple
            if label_list and isinstance(label_list, list):
                unique_labels.update(label_list)
        
        # Average processing time
        avg_time = db.query(func.avg(EmailRecord.processing_time_ms)).scalar() or 0
        
        # Processing rate (assume 98% for now, can be calculated based on success/failure)
        processing_rate = 98.5
        
        # Get changes from last period (simplified)
        yesterday = datetime.utcnow() - timedelta(days=1)
        emails_yesterday = db.query(EmailRecord).filter(EmailRecord.processed_at >= yesterday).count()
        
        return StatsResponse(
            total_emails=total_emails,
            total_labels=len(unique_labels),
            processing_rate=processing_rate,
            avg_processing_time=int(avg_time),
            emails_change=12.5,  # Mock data - calculate actual change
            labels_change=0.0,
            rate_change=2.1,
            time_change=-5.2
        )
    
    @staticmethod
    def get_label_trends(db: Session, days: int = 7) -> List[TrendData]:
        """Get label distribution trends over time"""
        since = datetime.utcnow() - timedelta(days=days)
        
        # Group emails by date and count labels
        trends = []
        for i in range(days):
            date = since + timedelta(days=i)
            date_str = date.strftime("%Y-%m-%d")
            
            # Get emails for this date
            day_start = date.replace(hour=0, minute=0, second=0, microsecond=0)
            day_end = day_start + timedelta(days=1)
            
            emails = db.query(EmailRecord).filter(
                and_(EmailRecord.processed_at >= day_start, EmailRecord.processed_at < day_end)
            ).all()
            
            # Count labels for this day
            label_counts = {
                'work': 0, 'personal': 0, 'spam': 0, 'important': 0,
                'promotional': 0, 'social': 0, 'family': 0, 'shopping': 0, 'tech': 0
            }
            
            for email in emails:
                if email.labels:
                    for label in email.labels:
                        if label in label_counts:
                            label_counts[label] += 1
            
            trends.append(TrendData(
                date=date_str,
                **label_counts
            ))
        
        return trends
    
    @staticmethod
    def update_stats(db: Session, labels: List[str], processing_time_ms: int = None):
        """Update system statistics after processing an email"""
        today = datetime.utcnow().date()
        
        # Update or create daily stats
        stats = db.query(SystemStats).filter(
            func.date(SystemStats.date) == today
        ).first()
        
        if not stats:
            stats = SystemStats(
                date=datetime.utcnow(),
                total_emails_processed=1,
                total_labels_applied=len(labels),
                avg_processing_time=processing_time_ms or 0
            )
            db.add(stats)
        else:
            stats.total_emails_processed += 1
            stats.total_labels_applied += len(labels)
            if processing_time_ms:
                # Update running average
                total_time = stats.avg_processing_time * (stats.total_emails_processed - 1) + processing_time_ms
                stats.avg_processing_time = int(total_time / stats.total_emails_processed)
        
        # Update label statistics
        for label in labels:
            label_stat = db.query(LabelStats).filter(
                and_(LabelStats.label_name == label, func.date(LabelStats.date) == today)
            ).first()
            
            if not label_stat:
                label_stat = LabelStats(
                    date=datetime.utcnow(),
                    label_name=label,
                    count=1
                )
                db.add(label_stat)
            else:
                label_stat.count += 1
        
        db.commit()
    
    @staticmethod
    def reclassify_email(db: Session, email_id: str, new_labels: List[str]) -> EmailResponse:
        """Reclassify an email with new labels"""
        email = db.query(EmailRecord).filter(EmailRecord.id == email_id).first()
        if not email:
            raise ValueError("Email not found")
        
        email.labels = new_labels
        email.processed_at = datetime.utcnow()
        db.commit()
        db.refresh(email)
        
        return EmailResponse(
            id=email.id,
            subject=email.subject,
            sender=email.sender,
            sender_name=email.sender_name,
            snippet=email.snippet,
            labels=email.labels or [],
            timestamp=email.timestamp,
            is_read=email.is_read,
            processing_time_ms=email.processing_time_ms
        )
